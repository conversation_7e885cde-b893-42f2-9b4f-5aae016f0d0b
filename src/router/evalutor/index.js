const importEvalutor = resolve => {
	require.ensure(
		["@/components/evalutor/import-evalutor.vue"],
		() => {
			resolve(require("@/components/evalutor/import-evalutor.vue"));
		},
		"import-evalutor"
	);
};
const importDOEPayments = resolve => {
	require.ensure(
		["@/components/evalutor/import-doe-payment.vue"],
		() => {
			resolve(require("@/components/evalutor/import-doe-payment.vue"));
		},
		"import-doe-payments"
	);
};
const manageEvalutor = resolve => {
	require.ensure(
		["@/components/evalutor/manage-evalutor/manage-evalutor.vue"],
		() => {
			resolve(require("@/components/evalutor/manage-evalutor/manage-evalutor.vue"));
		},
		"edit-evalutor"
	);
};
const openEvaluations = resolve => {
	require.ensure(
		["@/components/evalutor/open-evaluations/open-evaluations.vue"],
		() => {
			resolve(require("@/components/evalutor/open-evaluations/open-evaluations.vue"));
		},
		"open-evaluations"
	);
};
const cancelEvaluations = resolve => {
	require.ensure(
		["@/components/evalutor/cancel-evaluation/cancel-evaluation.vue"],
		() => {
			resolve(require("@/components/evalutor/cancel-evaluation/cancel-evaluation.vue"));
		},
		"cancel-evaluations"
	);
};
const markRevisionNeeded = resolve => {
	require.ensure(
		["@/components/evalutor/mark-revision-needed/mark-revision-needed.vue"],
		() => {
			resolve(require("@/components/evalutor/mark-revision-needed/mark-revision-needed.vue"));
		},
		"mark-revision-needed"
	);
};
const markEvaluatorPaid = resolve => {
	require.ensure(
		["@/components/evalutor/mark-evaluator-paid/mark-evaluator-paid.vue"],
		() => {
			resolve(require("@/components/evalutor/mark-evaluator-paid/mark-evaluator-paid.vue"));
		},
		"mark-evaluator-paid"
	);
};
const markRevisionCompleted = resolve => {
	require.ensure(
		["@/components/evalutor/mark-revision-completed/mark-revision-completed.vue"],
		() => {
			resolve(require("@/components/evalutor/mark-revision-completed/mark-revision-completed.vue"));
		},
		"mark-revision-completed"
	);
};
const markPaymentReceived = resolve => {
	require.ensure(
		["@/components/evalutor/mark-payment-received/mark-payment-received.vue"],
		() => {
			resolve(require("@/components/evalutor/mark-payment-received/mark-payment-received.vue"));
		},
		"mark-payment-received"
	);
};

const reportPaidEvaluators = resolve => {
	require.ensure(
		["@/components/evalutor/rpt-paid-evaluators/rpt-paid-evaluators.vue"],
		() => {
			resolve(require("@/components/evalutor/rpt-paid-evaluators/rpt-paid-evaluators.vue"));
		},
		"rpt-paid-evaluators"
	);
};

const rptToBeBilled = resolve => {
	require.ensure(
		["@/components/evalutor/rpt-to-be-billed/rpt-to-be-billed.vue"],
		() => {
			resolve(require("@/components/evalutor/rpt-to-be-billed/rpt-to-be-billed.vue"));
		},
		"rpt-to-be-billed"
	);
};

const rptPaidDoe = resolve => {
	require.ensure(
		["@/components/evalutor/rpt-paid-doe/rpt-paid-doe.vue"],
		() => {
			resolve(require("@/components/evalutor/rpt-paid-doe/rpt-paid-doe.vue"));
		},
		"rpt-paid-doe"
	);
};

const rptPastCompliance = resolve => {
	require.ensure(
		["@/components/evalutor/rpt-past-compliance/rpt-past-compliance.vue"],
		() => {
			resolve(require("@/components/evalutor/rpt-past-compliance/rpt-past-compliance.vue"));
		},
		"rpt-past-compliance"
	);
};

const assignedEvaluations = resolve => {
	require.ensure(
		["@/components/evalutor/assigned-evaluations/assigned-evaluations.vue"],
		() => {
			resolve(require("@/components/evalutor/assigned-evaluations/assigned-evaluations.vue"));
		},
		"assigned-evaluations"
	);
};

const paidToEvaluator = resolve => {
	require.ensure(
		["@/components/evalutor/paid-to-evaluator/paid-to-evaluator.vue"],
		() => {
			resolve(require("@/components/evalutor/paid-to-evaluator/paid-to-evaluator.vue"));
		},
		"paid-to-evaluator"
	);
};

const confirmEvaluations = resolve => {
	require.ensure(
		["@/components/evalutor/confirm-evaluations/confirm-evaluations.vue"],
		() => {
			resolve(require("@/components/evalutor/confirm-evaluations/confirm-evaluations.vue"));
		},
		"confirm-evaluations"
	);
};
const assignBillDate = resolve => {
	require.ensure(
		["@/components/evalutor/assign-bill-date/assign-bill-date.vue"],
		() => {
			resolve(require("@/components/evalutor/assign-bill-date/assign-bill-date.vue"));
		},
		"assign-bill-date"
	);
};
const markAsFlagged = resolve => {
	require.ensure(
		["@/components/evalutor/mark-as-flagged/mark-as-flagged.vue"],
		() => {
			resolve(require("@/components/evalutor/mark-as-flagged/mark-as-flagged.vue"));
		},
		"mark-as-flagged"
	);
};

const rptCasesFlagged = resolve => {
	require.ensure(
		["@/components/evalutor/rpt-cases-flagged/rpt-cases-flagged.vue"],
		() => {
			resolve(require("@/components/evalutor/rpt-cases-flagged/rpt-cases-flagged.vue"));
		},
		"rpt-cases-flagged"
	);
};

const setEvaluationsDate = resolve => {
	require.ensure(
		["@/components/evalutor/set-evaluations-date/set-evaluations-date.vue"],
		() => {
			resolve(require("@/components/evalutor/set-evaluations-date/set-evaluations-date.vue"));
		},
		"set-evaluations-date"
	);
};
const rptSearchEvaluations = resolve => {
	require.ensure(
		["@/components/evalutor/rpt-search-evaluations/rpt-search-evaluations.vue"],
		() => {
			resolve(require("@/components/evalutor/rpt-search-evaluations/rpt-search-evaluations.vue"));
		},
		"rpt_search_evaluations"
	);
};
const rptAssigned = resolve => {
	require.ensure(
		["@/components/evalutor/rpt-assigned/rpt-assigned.vue"],
		() => {
			resolve(require("@/components/evalutor/rpt-assigned/rpt-assigned.vue"));
		},
		"rpt-assigned"
	);
};
const rptThirtyCases = resolve => {
	require.ensure(
		["@/components/evalutor/rpt-thirty-cases/rpt-thirty-cases.vue"],
		() => {
			resolve(require("@/components/evalutor/rpt-thirty-cases/rpt-thirty-cases.vue"));
		},
		"rpt-thirty-cases"
	);
};


const layout = resolve => {
	require.ensure(
		["@/components/layout/container"],
		() => {
			resolve(require("@/components/layout/container"));
		},
		"layout"
	);
};
export default {
	path: "/",
	component: layout,
	children: [
		{
			path: "/import_evaluations",
			name: "import_evaluations",
			component: importEvalutor
		},
		{
			path: "/import_doe_payments",
			name: "import_doe_payments",
			component: importDOEPayments
		},

		{
			path: "/edit_evaluations",
			name: "edit_evaluations",
			component: manageEvalutor
		},
		{
			path: "/open_evaluations",
			name: "open-evaluations",
			component: openEvaluations
		},
		{
			path: "/cancel_evaluations",
			name: "cancel_evaluations",
			component: cancelEvaluations
		},
		{
			path: "/mark_evaluator_paid",
			name: "mark_evaluator_paid",
			component: markEvaluatorPaid
		},
		{
			path: "/mark_revision_needed",
			name: "mark_revision_needed",
			component: markRevisionNeeded
		},
		{
			path: "/mark_revision_completed",
			name: "mark_revision_completed",
			component: markRevisionCompleted
		},
		{
			path: "/mark_payment_received",
			name: "mark_payment_received",
			component: markPaymentReceived
		},
		{
			path: "/rpt_paid_evaluators",
			name: "rpt_paid_evaluators",
			component: reportPaidEvaluators
		},
		{
			path: "/rpt_to_be_billed",
			name: "rpt_to_be_billed",
			component: rptToBeBilled
		},
		{
			path: "/rpt_paid_doe",
			name: "rpt_paid_doe",
			component: rptPaidDoe
		},
		{
			path: "/rpt_past_compliance",
			name: "rpt_past_compliance",
			component: rptPastCompliance
		},
		{
			path: "/assigned_evaluations",
			name: "assigned_evaluations",
			component: assignedEvaluations
		},
		{
			path: "/paid_to_evaluator",
			name: "paid_to_evaluator",
			component: paidToEvaluator
		},


		{
			path: "/confirm_evaluations",
			name: "confirm_evaluations",
			component: confirmEvaluations
		},
		{
			path: "/assign_bill_date",
			name: "assign_bill_date",
			component: assignBillDate
		},
		{
			path: "/mark_as_flagged",
			name: "mark_as_flagged",
			component: markAsFlagged
		},
		{
			path: "/rpt_cases_flagged",
			name: "rpt_cases_flagged",
			component: rptCasesFlagged
		},
		{
			path: "/set_evaluations_date",
			name: "set_evaluations_date",
			component: setEvaluationsDate
		},
		{
			path: "/rpt_search_evaluations",
			name: "rpt_search_evaluations",
			component: rptSearchEvaluations
		},
		{
			path: "/rpt_assigned",
			name: "rpt_assigned",
			component: rptAssigned
		},
		{
			path: "/rpt_thirty_cases",
			name: "rpt_thirty_cases",
			component: rptThirtyCases
		},


	]
};
