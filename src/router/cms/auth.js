const login = resolve => {
	require.ensure(
		["@/components/cms/auth/login.vue"],
		() => {
			resolve(require("@/components/cms/auth/login.vue"));
		},
		"login"
	);
};

const forgotpassword = resolve => {
	require.ensure(
		["@/components/cms/auth/forgot-password.vue"],
		() => {
			resolve(require("@/components/cms/auth/forgot-password.vue"));
		},
		"forgotpassword"
	);
};

const resetpassword = resolve => {
	require.ensure(
		["@/components/cms/auth/reset-password.vue"],
		() => {
			resolve(require("@/components/cms/auth/reset-password.vue"));
		},
		"resetpassword"
	);
};
const permission = resolve => {
	require.ensure(
		["@/components/cms/403.vue"],
		() => {
			resolve(require("@/components/cms/403.vue"));
		},
		"permission"
	);
};
const confirmEvalution = resolve => {
	require.ensure(
		["@/components/evalutor/confirm-evalution.vue"],
		() => {
			resolve(require("@/components/evalutor/confirm-evalution.vue"));
		},
		"confirm-evalution"
	);
};


export default {
	routes: [
		{
			path: "/login",
			name: "Home",
			component: login
		},
		{
			path: "/forgot-password",
			name: "Forgot Password",
			component: forgotpassword
		},
		{
			path: "/reset_password",
			name: "Reset Password",
			component: resetpassword
		},
		{
			path: "/access-denied",
			name: "Access Denied",
			component: permission
		},
		{
			path: "/confirm-evalution/:token",
			name: "confirm-evalution",
			component: confirmEvalution
		}
	]
};
