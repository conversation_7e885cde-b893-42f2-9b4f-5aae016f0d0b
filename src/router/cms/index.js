const userTypeList = resolve => {
	require.ensure(
		["@/components/cms/user-types/list/index.vue"],
		() => {
			resolve(require("@/components/cms/user-types/list/index.vue"));
		},
		"user-type-list"
	);
};
const userTypeManage = resolve => {
	require.ensure(
		["@/components/cms/user-types/manage/index.vue"],
		() => {
			resolve(require("@/components/cms/user-types/manage/index.vue"));
		},
		"user-type-manage"
	);
};

const userList = resolve => {
	require.ensure(
		["@/components/cms/users/list/index.vue"],
		() => {
			resolve(require("@/components/cms/users/list/index.vue"));
		},
		"user-list"
	);
};
const userManage = resolve => {
	require.ensure(
		["@/components/cms/users/manage/index.vue"],
		() => {
			resolve(require("@/components/cms/users/manage/index.vue"));
		},
		"user-manage"
	);
};

const company = resolve => {
	require.ensure(
		["@/components/cms/company/index.vue"],
		() => {
			resolve(require("@/components/cms/company/index.vue"));
		},
		"company"
	);
};

const changepassword = resolve => {
	require.ensure(
		["@/components/cms/change-password/index.vue"],
		() => {
			resolve(require("@/components/cms/change-password/index.vue"));
		},
		"changepassword"
	);
};

const profile = resolve => {
	require.ensure(
		["@/components/cms/profile/index.vue"],
		() => {
			resolve(require("@/components/cms/profile/index.vue"));
		},
		"profile"
	);
};

const layout = resolve => {
	require.ensure(
		["@/components/layout/container"],
		() => {
			resolve(require("@/components/layout/container"));
		},
		"layout"
	);
};
export default {
	path: "/",
	component: layout,
	children: [
		{
			path: "/user-types",
			name: "User type list",
			component: userTypeList
		},
		{
			path: "/user-type/:id",
			name: "User type manage",
			component: userTypeManage
		},
		{
			path: "/users",
			name: "User list",
			component: userList
		},
		{
			path: "/user/:id",
			name: "User manage",
			component: userManage
		},
		{
			path: "company",
			name: "company manage",
			component: company
		},
		{
			path: "change-password",
			name: "Change Password",
			component: changepassword
		},
		{
			path: "profile",
			name: "Profile",
			component: profile
		}
	]
};
