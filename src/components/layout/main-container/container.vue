<template>
	<div
		class="page-container"
		:style="{
			'padding-left': $store.state.isOpenSideBar ? '260px' : '0px'
		}"
	>
		<top></top>
		<div class="main-content">
			<div class="section__content section__content--p30">
				<router-view />
			</div>
		</div>
	</div>
</template>
<script>
import top from "./top.vue";
export default {
	components: { top },
	mounted() {
		let width = document.documentElement.clientWidth;
		if (width <= 768) {
			this.$store.state.isOpenSideBar = false;
			this.$store.state.isMobileDevice = true;
		}
	}
};
</script>
