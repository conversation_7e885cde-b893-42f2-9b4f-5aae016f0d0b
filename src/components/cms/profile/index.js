export default {
	data: () => ({
		user: {
			userid: 0,
			username: "<PERSON><PERSON><PERSON>",
			usertype: "1",
			email: "<EMAIL>",
			profileimage: "images/icon/default-user-black.svg",
			imagefile:"",
			firstname: "Admin",
			lastname: "User",
			contact_no: "1234567890",
			facebook_url: "test",
			instagram_url: "test",
			twitter_url: "test",
			linkedin_url: "test",
			whatsapp_number: "0123456789",
			isActive: 0
		},
		usertyelist: [
			{
				id: "1",
				Name: "Admin",
				role: "Admin"
			},
			{
				id: "2",
				Name: "peter",
				role: "viceAdmin"
			},
			{
				id: "3",
				Name: "<PERSON>",
				role: "Member"
			}
		],
		error: "",
		backUrl: "/users"
	}),
	methods: {
		update() {
			let _vm = this;
			this.axios
				.post("/api/users/updateuser?id" + this.user.userid, {
					...this.user,
					_method: "PATCH"
				})
				.then(function(response) {
					_vm.user = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		selectImage(event){
			let _vm = this;
			var input = event.target;
			if (input.files && input.files[0]) {
				_vm.user.imagefile = input.files[0];
				var reader = new FileReader();
				reader.onload = function(e) {
					//_vm.user.profileimage = e.target.result;
					$('#imagePreview').css('background-image', 'url('+e.target.result +')');
					$('#imagePreview').hide();
					$('#imagePreview').fadeIn(650);
				}
				reader.readAsDataURL(input.files[0]);
			}
		},
		getDetail() {
			let _vm = this;
			this.axios
				.get("/api/users/getuser?id" + this.$route.params.id)
				.then(function(response) {
					_vm.user = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		}
	},
	mounted() {
		if (this.$route.params.id > 0) {
			this.getDetail();
		}
	}
};
