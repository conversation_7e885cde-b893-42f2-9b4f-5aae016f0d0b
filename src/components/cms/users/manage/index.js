export default {
	data: () => ({
		user: {
			id: 0,
			name: "",
			usertype_id: "",
			email: "",
			password: "",
			c_password: "",
			profileimage: "",
			firstname: "",
			lastname: "",
			contact_no: "",
			facebook_url: "",
			instagram_url: "",
			twitter_url: "",
			linkedin_url: "",
			whatsapp_number: "",
			isActive: 0
		},
		usertyelist: [],
		error: "",
		backUrl: "/users"
	}),
	methods: {
		resetForm() {
			this.$refs.observer.reset();
			this.user = {
				id: 0,
				name: "",
				usertype_id: "",
				email: "",
				password: "",
				c_password: "",
				profileimage: "",
				firstname: "",
				lastname: "",
				contact_no: "",
				facebook_url: "",
				instagram_url: "",
				twitter_url: "",
				linkedin_url: "",
				whatsapp_number: "",
				isActive: 0
			};
		},
		validateUser() {
			this.user.id > 0 ? this.update() : this.add();
		},
		update() {
			let _vm = this;
			this.axios
				.post("/users/" + this.user.id, {
					...this.user,
					_method: "PATCH"
				})
				.then(function(response) {
					_vm.user = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		add() {
			let _vm = this;
			this.axios
				.post("/users", this.user)
				.then(function() {
					_vm.$router.push(_vm.backUrl);
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getDetail() {
			let _vm = this;
			this.axios
				.get("/users/" + this.$route.params.id)
				.then(function(response) {
					_vm.user = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getQueryString() {
			let queryString = "?search=";
			return queryString;
		},
		getUserType() {
			let _vm = this;
			let queryString = this.getQueryString();
			this.axios
				.get("/user-types" + queryString)
				.then(function(response) {
					_vm.usertyelist = response.data.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		selectUserImage(event){
			let _vm = this;
			var input = event.target;
			if (input.files && input.files[0]) {
				_vm.user.profileimage = input.files[0];
				var reader = new FileReader();
				reader.onload = function(e) {
					//_vm.user.profileimage = e.target.result;
					$('#imagePreview').css('background-image', 'url('+e.target.result +')');
					$('#imagePreview').hide();
					$('#imagePreview').fadeIn(650);
				}
				reader.readAsDataURL(input.files[0]);
			}
		}
	},
	mounted() {
		this.getUserType();
		if (this.$route.params.id > 0) {
			this.getDetail();
		}
	}
};
