export default {
	data: () => ({
		company: {
			id: 1,
			name: "",
			email: "",
			email2: "",
			contact_no: "",
			contact_no2: "",
			address: "",
			city: "Nav1",
			state: "guj1",
			country: "1",
			pincode: "",
			whatsapp: "",
			facebook: "",
			google: "",
			meta_title: "",
			meta_keyword: "",
			meta_description: ""
		},
		country_list: [
			{
				country_id: "1",
				name: "India"
			},
			{
				country_id: "2",
				name: "US"
			},
			{
				country_id: "3",
				name: "UK"
			}
		],
		state_list: [],
		city_list: [],
		error: ""
	}),
	methods: {
		resetForm() {
			this.$refs.observer.reset();
			this.getDetail();
		},
		validateCompany() {
			this.update();
		},
		update() {
			let _vm = this;
			this.axios
				.post("/company/update", this.company)
				.then(function(response) {
					_vm.company = response.data.data[0];
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		selectImage(event) {
			let _vm = this;
			var input = event.target;
			if (input.files && input.files[0]) {
				_vm.company.imagefile = input.files[0];
				var reader = new FileReader();
				reader.onload = function(e) {
					//_vm.user.profileimage = e.target.result;
					$("#imagePreview").css(
						"background-image",
						"url(" + e.target.result + ")"
					);
					$("#imagePreview").hide();
					$("#imagePreview").fadeIn(650);
				};
				reader.readAsDataURL(input.files[0]);
			}
		},
		getDetail() {
			let _vm = this;
			this.axios
				.get("/company/getcompany")
				.then(function(response) {
					console.log(response.data.data);
					_vm.company = response.data.data[0];
					_vm.company.id = 1;
				})
				.catch(function(error) {
					console.log("error:" + error);
				});
		},
		getCountry() {
			let _vm = this;
			this.axios
				.get("/country")
				.then(function(response) {
					_vm.country_list = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getState() {
			let _vm = this;
			_vm.state_list = [];
			// _vm.company.state = "";
			// _vm.company.city = "";
			// this.axios
			// 	.get("/state/" + _vm.company.country)
			// 	.then(function(response) {
			// 		_vm.state_list = response.data.data;
			// 	})
			// 	.catch(function(error) {
			// 		console.log(error);
			// 	});
			_vm.state_list = [
				{
					country_id: "1",
					state_id: 1,
					name: "Gujarat"
				},
				{
					country_id: "1",
					state_id: 2,
					name: "Maharastra"
				},
				{
					country_id: "1",
					state_id: 3,
					name: "Punjab"
				}
			];
		},
		getCity() {
			let _vm = this;
			_vm.city_list = [];
			// _vm.company.city = "";
			// this.axios
			// 	.get("/city/" + _vm.company.state)
			// 	.then(function(response) {
			// 		_vm.city_list = response.data.data;
			// 	})
			// 	.catch(function(error) {
			// 		console.log(error);
			// 	});
			_vm.city_list = [
				{
					city_id: "1",
					state_id: 1,
					name: "Navsari"
				},
				{
					city_id: "2",
					state_id: 1,
					name: "Surat"
				},
				{
					city_id: "3",
					state_id: 1,
					name: "Valsad"
				}
			];
		}
	},
	mounted() {
		this.getDetail();
		if (this.$route.params.id > 0) {
			this.getCountry();
		}
	}
};
