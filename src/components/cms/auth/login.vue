<template>
	<div class="page-content--bge5">
		<div class="container">
			<div class="login-wrap">
				<div class="login-content">
					<div class="login-logo">
						<h3>User Login</h3>
					</div>
					<div class="login-form">
						<ValidationObserver v-slot="{ handleSubmit }">
							<form @submit.prevent="handleSubmit(validateUser)">
								<div class="form-group">
									<label>Email Address</label>
									<validation-provider
										name="User Name"
										rules="required"
										v-slot="{ errors }"
									>
										<input
											class="au-input au-input--full form-control"
											type="text"
											name="user_name"
											v-model="login.user_name"
											placeholder="User Name"
										/>
										<span class="invalid-feedback">{{
											errors[0]
										}}</span>
									</validation-provider>
								</div>
								<div class="form-group">
									<label>Password</label>
									<validation-provider
										name="Password"
										rules="required"
										v-slot="{ errors }"
									>
										<input
											class="au-input au-input--full"
											type="password"
											name="password"
											v-model="login.password"
											placeholder="Password"
										/>
										<span class="invalid-feedback">{{
											errors[0]
										}}</span>
									</validation-provider>
								</div>
								<div class="login-checkbox">
									<label>
										<input
											type="checkbox"
											name="remember"
										/>Remember Me
									</label>
									<label>
										<a href="#">Forgotten Password?</a>
									</label>
								</div>
								<button
									class="au-btn au-btn--block au-btn--green m-b-20"
									type="submit"
								>
									sign in
								</button>
							</form>
						</ValidationObserver>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script src="./login.js"></script>
