export default {
	data: () => ({
		login: {
			user_name: "",
			password: "",
			confirmpassword: ""
		},
		error: ""
	}),
	methods: {
		validateResetPassword() {
			this.axios
				.post("/login", {
					email: this.login.user_name,
					password: this.login.password,
					confirmpassword: this.login.confirmpassword
				})
				.then(function(response) {
					console.log("successs");
					localStorage.setItem(
						process.env.VUE_APP_TOKEN_NAME,
						response.data.data.token
					);
					window.location.href = "/";
				})
				.catch(function(error) {
					console.log(error);
				});
		}
	}
};
