var sha224 = require('js-sha256').sha224;
export default {
	data: () => ({
		user: {},
		login: {
			user_name: "",
			password: ""
		}
	}),
	props: {
		source: String
	},
	methods: {
		validateUser() {
			let formData = new FormData();
			formData.append('email', this.login.user_name);
			formData.append('password', sha224(this.login.password));
			const _vm = this;
			this.axios
				.post("/verify_user.php", formData)
				.then(function(response) {
						localStorage.setItem(
							process.env.VUE_APP_TOKEN_NAME,
							JSON.stringify(response.data.token));
							_vm.axios
							.get("/get_navbar_menu_items.php")
							.then(function(menu) {
								window.location.href = menu.data.data.menu[0].url;
							});
				})
				.catch(function(error) {
					console.log(error);
				});
		}
	}
};
