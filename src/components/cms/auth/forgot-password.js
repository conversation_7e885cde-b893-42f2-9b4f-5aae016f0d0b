export default {
	data: () => ({
		login: {
			user_name: ""
		},
		error: ""
	}),
	methods: {
		validateForgotPassword() {
			this.sendLink();
		},
		sendLink() {
			let _vm = this;
			this.axios
				.post("/api/forgot-password?email?" + this.user.userid, {
					...this.login,
					_method: "PATCH"
				})
				.then(function(response) {
					_vm.login = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
	}
};
