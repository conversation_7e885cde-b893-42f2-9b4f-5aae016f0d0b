
export default {
	data: () => ({
		evaluations:[],
	}),
	methods: {
		getOpenEvaluations() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_report_evaluators_30_open_cases.php")
				.then(function(response) {
					_vm.showLoader = false;
					_vm.evaluations = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		
	},
	mounted() {
		this.getOpenEvaluations();
	}
};
