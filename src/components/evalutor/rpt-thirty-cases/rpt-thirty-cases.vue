<template>
	<div>
		<div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Evaluators with 30+ Open Cases</h2>
		</div>
		<div class="card">
			<div class="card-body p-0">
				<div class="form-group">
					<div class="col-md-6" style="overflow:auto">
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="50%" >Evaluator</th>
									<th width="50%" ># of Open Cases</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(evaluation, index) in evaluations" :key="index">
									<td>{{ evaluation.evaluator_name }}</td>
									<td>{{ evaluation.number_of_open_cases }}</td>
								</tr>
								<tr>
									<td colspan="2" v-if="evaluations.length == 0">
										<center><strong>No Record Found.</strong></center>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script src="./rpt-thirty-cases.js" />