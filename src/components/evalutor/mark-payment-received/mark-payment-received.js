
// import headerList from "@/components/common/header-list.vue";
import { paginationMixin } from "@/mixins/paginationMixin";
export default {
	data: () => ({
		selectedEvaluator:{},
		evaluator:{},
		comments:"",
		evaluations:[],
		showLoader: false
	}),
	mixins: [paginationMixin],
	computed: {
		selectedList() {
			return this.evaluations.filter(element => element.isSelected==true);
		}
	},
	methods: {
		resetForm() {
			this.$refs.observer.reset();
			this.evaluator = {
				id: 0,
			};
		},
		submit() {
			let _vm = this;
			let fd = new FormData();
			let list = [];
			this.selectedList.forEach(element => {
				list.push(element.id);
			});
			fd.append("evaluation_ids",list.join(','));
			this.axios
				.post("/set_evaluations_as_payment_received.php", fd)
				.then(function() {
					_vm.comments="";
					_vm.selectedEvaluator ={};
					_vm.getOpenEvaluations();
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getOpenEvaluations() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_evaluations.php?eval_selection_type=Payment-Received")
				.then(function(response) {
					_vm.showLoader = false;
					response.data.data.forEach(element => {
						element["isSelected"] = false;
					});
					_vm.evaluations = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		
	},
	mounted() {
		this.getOpenEvaluations();
	}
};
