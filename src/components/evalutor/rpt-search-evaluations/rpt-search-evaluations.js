
import { paginationMixin } from "@/mixins/paginationMixin";
import Datepicker from 'vuejs-datepicker';
export default {
	data: () => ({
		selectedEvaluator:{},
		evaluator:{},
		comments:"",
		evaluators: [],
		statesList:[],
		evaluations:[],
		dateTypeList:[],
		searchTypeList:[],
		searchTypeId:0,
		dateType:0,
		error: "",
		searchText: "",
		showLoader: false,
		fromDate : new Date(),
		toDate : new Date(),
	}),
	components: {
		Datepicker
	},
	mixins: [paginationMixin],
	computed: {
		selectedStudent() {
			return this.evaluations.filter(element => element.isSelected==true);
		}
	},
	methods: {
		resetForm() {
			this.$refs.observer.reset();
			this.evaluator = {
				id: 0,
			};
		},
		cancelEvalutions() {
			let _vm = this;
			let fd = new FormData();
			let list = [];
			this.selectedStudent.forEach(element => {
				list.push(element.id);
			});
			fd.append("comments",this.comments);
			fd.append("evaluation_ids",list.join(','));
			this.axios
				.post("/set_cancel_evaluations.php", fd)
				.then(function() {
					_vm.comments="";
					_vm.getOpenEvaluations();
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		convertDate(value){
			const date = new Date(value);
			return date.getFullYear() + "-" + ((date.getMonth() + 1) > 9 ? '' : '0') + (date.getMonth() + 1) +"-"+ (date.getDate() > 9 ? '' : '0') + date.getDate();
		},
		getDateTypes() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_date_search_types.php?date_search_type_id=1&date_search_type_desc")
				.then(function(response) {
					_vm.showLoader = false;
					_vm.dateTypeList = response.data.data;
					_vm.dateType =_vm.dateTypeList[0].date_search_type_id;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getSearchType() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_text_search_types.php?text_search_type_id=1&text_search_type_desc")
				.then(function(response) {
					_vm.showLoader = false;
					_vm.searchTypeList = response.data.data;
					_vm.searchTypeId =_vm.searchTypeList[0].text_search_type_id; 
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getOpenEvaluations() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_search_evaluations.php?from_date="+this.convertDate(this.fromDate)+"&to_date="+ this.convertDate(this.toDate)
					+"&date_search_type_id="+this.dateType+"&text_search_type_id="+this.searchTypeId+"&text_search="+this.searchText)
				.then(function(response) {
					_vm.showLoader = false;
					response.data.data.forEach(element => {
						element["isSelected"] = false;
					});
					_vm.evaluations = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		
	},
	mounted() {
		this.getSearchType();
		this.getDateTypes();
		//this.getOpenEvaluations();
	}
};
