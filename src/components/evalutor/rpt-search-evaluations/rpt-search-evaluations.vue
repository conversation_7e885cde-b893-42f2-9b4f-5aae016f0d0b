<template>
	<div>
		<div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Search Evaluations</h2>
		</div>
		<div class="card">
			<div class="card-body p-0">
				<div class="form-group pl-4 pt-4 row">
					<div class="col-md-2">
						<label>From Date:</label>
						<datepicker placeholder="From Date" format="MM/dd/yyyy" v-model="fromDate" class="form-control"></datepicker>
					</div>
					<div class="col-md-2">
						<label>To Date:</label>
						<datepicker placeholder="To Date" format="MM/dd/yyyy" class="form-control" v-model="toDate"></datepicker>
					</div>
					<div class="col-md-2">
						<label>Date Type:</label>
						<select class="form-control" v-model="dateType">
							<option v-for="(dateType,index) in dateTypeList" :key="index" :value="dateType.date_search_type_id">{{dateType.date_search_type_desc}}</option>
						</select>
					</div>
				</div>
				<div class="form-group row pl-4">
					<div class="col-md-4">
						<input type="text" v-model="searchText" class="au-input au-input--full form-control"  />
					</div>
					<div class="col-md-2 pr-0 pl-0">
						<select class="form-control" v-model="searchTypeId">
							<option v-for="(dateType,index) in searchTypeList" :key="index" :value="dateType.text_search_type_id">{{dateType.text_search_type_desc}}</option>
						</select>
					</div>
					<div class="col-md-4">
						<button data-toggle="tooltip" data-placement="top" type="button" title="Search Evalutions" @click="getOpenEvaluations" class="btn mr-2 border-none btn-outline-primary btn-md btn-primary">Search </button>
					</div>
				</div>
				<div class="form-group">
					<div class="col-md-12" style="overflow:auto">
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="10%" >Invoice #</th>
									<th width="10%" >Invoice Date</th>
									<th width="10%" >Evaluator</th>
									<th width="10%" >Student</th>
									<th width="10%" >Student DOB</th>
									<th width="10%" >School</th>
									<th width="10%" >Language</th>
									<th width="10%" >CSE</th>
									<th width="10%" >CSE Distr</th>
									<th width="10%" >Date of Eval.</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(evaluation, index) in evaluations" :key="index">
									<td>{{ evaluation.invoice_number }}</td>
									<td>{{ evaluation.invoice_date }}</td>
									<td>{{ evaluation.evalutor_name }}</td>
									<td>{{ evaluation.student_name }}</td>
									<td>{{ evaluation.student_dob }}</td>
									<td>{{ evaluation.student_name }}</td>
									<td>{{ evaluation.language }}</td>
									<td>{{ evaluation.cse }}</td>
									<td>{{ evaluation.cse_district }}</td>
									<td>{{ evaluation.compliance_date }}</td>
								</tr>
								<tr>
									<td colspan="10" v-if="evaluations.length == 0">
										<center><strong>No Record Found.</strong></center>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script src="./rpt-search-evaluations.js" />
<style scoped>
.vdp-datepicker{
	/* overflow: hidden; */
}
</style>