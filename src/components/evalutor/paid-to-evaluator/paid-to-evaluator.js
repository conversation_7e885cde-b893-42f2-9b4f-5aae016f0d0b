
import { paginationMixin } from "@/mixins/paginationMixin";
import Datepicker from 'vuejs-datepicker';
export default {
	data: () => ({
		selectedEvaluator:{},
		evaluator:{},
		comments:"",
		evaluators: [],
		statesList:[],
		evaluations:[],
		error: "",
		searchText: "",
		showLoader: false,
	}),
	components: {
		Datepicker
	},
	mixins: [paginationMixin],
	computed: {
		selectedStudent() {
			return this.evaluations.filter(element => element.isSelected==true);
		}
	},
	methods: {
		getOpenEvaluations() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_evaluations.php?eval_selection_type=Paid-To-Evaluator")
				.then(function(response) {
					_vm.showLoader = false;
					response.data.data.forEach(element => {
						element["isSelected"] = false;
					});
					_vm.evaluations = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},

	},
	mounted() {
		this.getOpenEvaluations();
	}
};
