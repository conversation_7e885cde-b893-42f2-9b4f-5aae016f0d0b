<template>
	<div>
		<div class="row">
			<div class="col-lg-12">
				<div class="card">
					<form method="post">
						<div class="col-6">
							<label for="provider" class="form-label" v-if="evaluations.length>0">Evaluator: {{ evaluations[0].evaluator_name }}
								<span class="text-danger"></span></label>
						</div>
						<div class="col-6">
							<p>Please Select Evaluations you are intrested in covering</p>
						</div>
						<div class="card-body" style="overflow:auto">
							<table class="table table-striped">
								<thead>
									<tr>
										<th width="5%" scope="col"></th>
										<th width="25%" scope="col">Student</th>
										<th width="10%" scope="col">DOB</th>
										<th width="15%" scope="col">School</th>
										<th width="10%" scope="col">Zip Code</th>
										<th width="10%" scope="col">District</th>
										<th width="10%" scope="col">Compl. Date</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="(evaluation, index) in evaluations" :key="index">
										<td><input type="checkbox" name="evaluation[]" v-model="evaluation.isSelected"
												:value="evaluation.id">
										</td>
										<td>{{ evaluation.student_name }} </td>
										<td>{{ evaluation.student_dob }}</td>
										<td>{{ evaluation.school_name }}</td>
										<td>{{ evaluation.zip_code ? evaluation.zip_code : '' }}</td>
										<td>{{ evaluation.district_name }}</td>
										<td>{{ evaluation.compliance_date | formatDate }}</td>
									</tr>
								</tbody>
							</table>

							<input type="button" id="submit" @click="submit()" class="btn btn-outline-primary" value="Submit to Agency" />
							<!-- End Table with stripped rows -->
						</div>
					</form>
					<!-- End Table with stripped rows -->
				</div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	data: () => ({
		evaluator: {},
		evaluations: []
	}),
	computed: {
		selectedStudent() {
			return this.evaluations.filter(element => element.isSelected==true);
		}
	},
	methods: {
		getAllEvaluators() {
			let _vm = this;
			this.axios
				.get("/get_evaluations_coverage_interest.php?token_url=" + this.$route.params.token)
				.then(function (response) {
						response.data.data.forEach(element => {
						element["isSelected"] = false;
					});
					_vm.evaluations = response.data.data;
				})
				.catch(function (error) {
					console.log(error);
				});
		},
		submit() {
			let _vm = this;
			let fd = new FormData();
			let list = [];
			this.selectedStudent.forEach(element => {
				list.push(element.evaluation_id);
			});
			fd.append("request_header_id",this.evaluations[0].request_header_id);
			fd.append("evaluation_ids",list.join(','));
			this.axios
				.post("/set_evaluations_as_evalutor_interested.php", fd)
				.then(function() {
					_vm.comments="";
					window.location.href="/login";
				})
				.catch(function(error) {
					console.log(error);
				});
		},
	},
	mounted() {
		this.getAllEvaluators();
	}
};
</script>

