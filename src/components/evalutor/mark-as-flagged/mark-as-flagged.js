
// import headerList from "@/components/common/header-list.vue";
import { paginationMixin } from "@/mixins/paginationMixin";
export default {
	data: () => ({
		selectedEvaluator:{},
		evaluator:{},
		comments:"",
		evaluators: [],
		statesList:[],
		evaluations:[],
		error: "",
		searchText: "",
		showLoader: false
	}),
	mixins: [paginationMixin],
	// components: { "header-list": headerList },
	computed: {
		selectedStudent() {
			return this.evaluations.filter(element => element.isSelected==true);
		}
	},
	methods: {
		resetForm() {
			this.$refs.observer.reset();
			this.evaluator = {
				id: 0,
			};
		},
		changeSelectedEvalution(evaluation){
			this.comments = evaluation.comments;
		},
		submit() {
			let _vm = this;
			let fd = new FormData();
			
			fd.append("comments",this.comments);
			fd.append("evaluation_id",this.selectedEvaluator.id);
			this.axios
				.post("/set_evaluations_as_flagged.php", fd)
				.then(function() {
					_vm.comments="";
					_vm.selectedEvaluator={};
					_vm.getOpenEvaluations();
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getOpenEvaluations() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_evaluations.php?eval_selection_type=Not-Flagged")
				.then(function(response) {
					_vm.showLoader = false;
					_vm.evaluations = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		
	},
	mounted() {
		this.getOpenEvaluations();
	}
};
