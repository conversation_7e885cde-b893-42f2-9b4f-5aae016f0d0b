<template>
	<div>
		<div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Assign Bill Date</h2>
		</div>
		<div class="card">
			<div class="card-body p-0" >
				<div class="form-group p-4 row">
					<div class="col-md-2">
						<label>Select Bill Date:</label>
					</div>

						<datepicker placeholder="From Date" calendar-button calendar-button-icon="fa fa-calendar" format="MM/dd/yyyy" v-model="fromDate" class="form-control col-md-1"></datepicker>
					<div class="col-md-4">
						<input v-if="selectedStudent.length > 0" type="submit" id="submit" class="btn btn-outline-primary"
						value="Assign Bill Date" @click="submit()">
					</div>
				</div>
				<table class="table table-striped" style="overflow-x:auto">
					<thead>
						<tr>
							<th width="5%" scope="col"></th>
							<th width="7%" >Invoice #</th>
							<th width="10%" >Invoice Date</th>
							<th width="10%" >Evaluator</th>
							<th width="10%" >Student</th>
							<th width="10%" >Student DOB</th>
							<th width="10%" >School</th>
							<th width="10%" >Language</th>
							<th width="10%" >CSE</th>
							<th width="8%" >CSE Distr</th>
							<th width="10%" >Date of Eval.</th>
						</tr>
					</thead>
				</table>
				<div class="scroll-table">
					<table class="table table-striped">
						<tbody :class="evaluations.length == 0?'w-100 d-inline-block':''">
							<tr v-for="(evaluation, index) in evaluations" :key="index">
								<td width="5%"><input type="checkbox" name="evaluation[]" v-model="evaluation.isSelected"
										:value="evaluation.id">
								</td>
								<td width="7%">{{ evaluation.student_name }}</td>
								<td width="10%">{{ evaluation.student_dob }}</td>
								<td width="10%">{{ evaluation.evaluator_name }}</td>
								<td width="10%">{{ evaluation.student_name }}</td>
								<td width="10%">{{ evaluation.student_dob }}</td>
								<td width="10%">{{ evaluation.school_name }}</td>
								<td width="10%">{{ evaluation.cse}}</td>
								<td width="8%">{{ evaluation.district_name }}</td>
								<td width="10%">{{ evaluation.compliance_date | formatDate }}</td>
							</tr>
							<tr class="w-100 d-inline-block">
								<td colspan="11" class="w-100 d-inline-block" v-if="evaluations.length == 0">
									<center><strong>No Record Found.</strong></center>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div class="p-4">
					<input v-if="selectedStudent.length > 0" type="submit" id="submit" class="btn btn-outline-primary"
						value="Assign Bill Date" @click="submit()">
				</div>
			</div>
		</div>
	</div>
</template>
<script src="./assign-bill-date.js" />
