
import { paginationMixin } from "@/mixins/paginationMixin";
import Datepicker from 'vuejs-datepicker';
export default {
	data: () => ({
		selectedEvaluator:{},
		evaluator:{},
		evaluators: [],
		statesList:[],
		evaluations:[],
		error: "",
		searchText: "",
		showLoader: false,
		fromDate:new Date()
	}),
	components: {
		Datepicker
	},
	mixins: [paginationMixin],
	computed: {
		selectedStudent() {
			return this.evaluations.filter(element => element.isSelected==true);
		}
	},
	methods: {
		convertDate(value){
			const date = new Date(value);
			return date.getFullYear() + "-" + ((date.getMonth() + 1) > 9 ? '' : '0') + (date.getMonth() + 1) +"-"+ (date.getDate() > 9 ? '' : '0') + date.getDate();
		},
		resetForm() {
			this.$refs.observer.reset();
			this.evaluator = {
				id: 0,
			};
		},
		submit() {
			let _vm = this;
			let fd = new FormData();
			let list = [];
			this.selectedStudent.forEach(element => {
				list.push(element.id);
			});
			fd.append("bill_date",this.convertDate(this.fromDate));
			fd.append("evaluation_ids",list.join(','));
			this.axios
				.post("/set_assign_bill_date.php", fd)
				.then(function() {
					_vm.getOpenEvaluations();
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getOpenEvaluations() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_evaluations.php?eval_selection_type=No_Bill_Date")
				.then(function(response) {
					_vm.showLoader = false;
					response.data.data.forEach(element => {
						element["isSelected"] = false;
					});
					_vm.evaluations = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		
	},
	mounted() {
		this.getOpenEvaluations();
	}
};
