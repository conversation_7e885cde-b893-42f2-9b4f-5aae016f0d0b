
import { paginationMixin } from "@/mixins/paginationMixin";
import { _ } from "vue-underscore";
export default {
	data: () => ({
		selectedEvaluator:{},
		evaluator:{},
		comments:"",
		evaluators: [],
		statesList:[],
		evaluations:[],
		tableData:[],
		error: "",
		searchText: "",
		showLoader: false
	}),
	mixins: [paginationMixin],
	computed: {
		selectedStudent() {
			return this.evaluations.filter(element => element.isSelected==true);
		}
	},
	methods: {
		searchData(){
			const _vm =this;
			this.tableData = _.filter(this.evaluations,function(element){
				return (element.student_name.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1) || 
				(element.district_name.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1) ||
				(element.school_name.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1) ||
				(element.student_dob.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1) ||
				(element.compliance_date.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1) ||
				(element.zip_code.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1);
			})
		},
		resetForm() {
			this.$refs.observer.reset();
			this.evaluator = {
				id: 0,
			};
		},
		cancelEvalutions() {
			let _vm = this;
			let fd = new FormData();
			let list = [];
			this.selectedStudent.forEach(element => {
				list.push(element.id);
			});
			fd.append("comments",this.comments);
			fd.append("evaluation_ids",list.join(','));
			this.axios
				.post("/set_cancel_evaluations.php", fd)
				.then(function() {
					_vm.comments="";
					_vm.getOpenEvaluations();
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getOpenEvaluations() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_evaluations.php?eval_selection_type=Active")
				.then(function(response) {
					_vm.showLoader = false;
					response.data.data.forEach(element => {
						element["isSelected"] = false;
					});
					_vm.evaluations = response.data.data;
					_vm.tableData = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		
	},
	mounted() {
		this.getOpenEvaluations();
	}
};
