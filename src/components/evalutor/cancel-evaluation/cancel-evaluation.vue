<template>
	<div>
		<div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Cancel Evaluations</h2>
			<div class="col-md-6 text-right">
				<div class="input-group input-group w-50 float-right">
					<input type="text" v-model="searchText" v-on:keyup.enter="searchData" class="form-control au-input" placeholder="Search ...">
					<div class="input-group-append bg-white">
						<button @click="searchData()" class=" btn btn-outline-primary" id="basic-addon2"><i
								class="fas fa-search"></i></button>
					</div>
				</div>
			</div>
		</div>
		<div class="card">
			<div class="card-body p-0">
				<table class="table table-striped">
					<thead>
						<tr>
							<th width="5%" scope="col"></th>
							<th width="25%" scope="col">Student</th>
							<th width="10%" scope="col">DOB</th>
							<th width="25%" scope="col">School</th>
							<th width="10%" scope="col">Zip Code</th>
							<th width="15%" scope="col">District</th>
							<th width="10%" scope="col">Compl. Date</th>
						</tr>
					</thead>
				</table>
				<div class="scroll-table">
					<table class="table table-striped">
						<tbody :class="tableData.length == 0 ? 'w-100 d-inline-block' : ''">
							<tr v-for="(evaluation, index) in tableData" :key="index">
								<td width="5%"><input type="checkbox" name="evaluation[]"
										v-model="evaluation.isSelected" :value="evaluation.id">
								</td>
								<td width="25%">{{ evaluation.student_name }}</td>
								<td width="10%">{{ evaluation.student_dob }}</td>
								<td width="25%">{{ evaluation.school_name }}</td>
								<td width="10%">{{ (evaluation.zip_code) ? evaluation.zip_code : '' }}
								</td>
								<td width="15%">{{ evaluation.district_name }}</td>
								<td width="10%">{{ evaluation.compliance_date | formatDate }}</td>
							</tr>
							<tr class="w-100 d-inline-block">
								<td colspan="7" width="100%" class="w-100 d-inline-block text-center"
									v-if="tableData.length == 0">
									<center><strong>No Record Found.</strong></center>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div class="p-4">
					<div class="form-group">
						<label>Comments</label>
						<textarea class="au-input au-input--full form-control" rows="3" v-model="comments"></textarea>
					</div>
					<input v-if="selectedStudent.length > 0" type="submit" id="submit" class="btn btn-outline-primary"
						value="Cancel Evalution(s)" @click="cancelEvalutions()">
				</div>
			</div>
		</div>
	</div>
</template>
<script src="./cancel-evaluation.js" />