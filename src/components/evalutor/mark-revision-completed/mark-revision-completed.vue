<template>
	<div>
		<div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Mark Revision Completed</h2>
		</div>
		<div class="card">
			<div class="card-body p-0"  style="overflow:auto">
				<table class="table table-striped">
					<thead>
						<tr>
							<th width="5%" scope="col"></th>
							<th width="25%" scope="col">Student</th>
							<th width="10%" scope="col">DOB</th>
							<th width="25%" scope="col">School</th>
							<th width="10%" scope="col">Zip Code</th>
							<th width="15%" scope="col">District</th>
							<th width="10%" scope="col">Compl. Date</th>
						</tr>
					</thead>
				</table>
				<table class="table table-striped scroll-table">
					<tbody class="w-100 d-inline-block">
						<tr v-for="(evaluation, index) in evaluations" :key="index">
							<td width="5%"><input type="radio" name="evaluation" v-model="selectedEvaluator"
									:value="evaluation">
							</td>
							<td width="25%">{{ evaluation.student_name }}</td>
							<td width="10%">{{ evaluation.student_dob }}</td>
							<td width="25%">{{ evaluation.school_name }}</td>
							<td width="10%">{{ (evaluation.zip_code) ? evaluation.zip_code : '' }}
							</td>
							<td width="15%">{{ evaluation.district_name }}</td>
							<td width="10%">{{ evaluation.compliance_date | formatDate }}</td>
						</tr>
						<tr class="w-100 d-inline-block">
							<td colspan="7" class="w-100 d-inline-block" v-if="evaluations.length == 0">
								<center><strong>No Record Found.</strong></center>
							</td>
						</tr>
					</tbody>
				</table>
				<div class="p-4">
					<div class="form-group">
						<label>Revision Comments</label>
						<textarea class="au-input au-input--full form-control" rows="3" v-model="comments"></textarea>
					</div>
					<input v-if="Object.keys(selectedEvaluator).length > 0" type="submit"
					id="submit" class="btn btn-outline-primary"
					value="Mark as Revision Complete" @click="submit()">
				</div>
			</div>
		</div>
	</div>
</template>
<script src="./mark-revision-completed.js" />