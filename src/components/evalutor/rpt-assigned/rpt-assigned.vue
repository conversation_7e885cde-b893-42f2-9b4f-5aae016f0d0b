<template>
	<div>
		<div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Assigned Cases by Evaluator</h2>
		</div>
		<div class="card">
			<div class="card-body p-0">
				<div class="form-group pl-4 pt-4 row">
					<div class="col-md-1 pr-0">
						<label>Evaluator:</label>
					</div>
					<div class="col-md-4">
						<select class="au-input au-input--full form-control" id="type" name="type"
							v-model="selectedEvaluator" @change="changeEvaluator()">
							<option v-for="(evaluator, index) in evaluatorList" :key="index"
								:value="evaluator.id">
								{{ evaluator.last_name }} {{ evaluator.first_name }}
							</option>
						</select>
					</div>
				</div>
				<div class="form-group pl-4 row">
					<div class="col-md-1 pr-0">
						<label>From Date:</label>
					</div>

					<datepicker placeholder="From Date" calendar-button calendar-button-icon="fa fa-calendar" format="MM/dd/yyyy" v-model="fromDate" class="form-control col-md-1"></datepicker>

					<div class="col-md-1 pr-0">
						<label>To Date:</label>
					</div>

					<datepicker placeholder="To Date" calendar-button calendar-button-icon="fa fa-calendar" format="MM/dd/yyyy" v-model="toDate" class="form-control col-md-1"></datepicker>

				</div>
				<div class="form-group pl-4 row">
					<div class="col-md-12 pr-0">
						<button data-toggle="tooltip" data-placement="top" type="button" title="Search" @click="getOpenEvaluations" class="btn mr-2 border-none btn-outline-primary btn-md btn-primary">Search </button>
					</div>
				</div>
				<div class="form-group">
					<div class="col-md-12" style="overflow:auto">
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="10%" >Invoice #</th>
									<th width="10%" >Invoice Date</th>
									<th width="10%" >Evaluator</th>
									<th width="10%" >Student</th>
									<th width="10%" >Student DOB</th>
									<th width="10%" >School</th>
									<th width="10%" >Language</th>
									<th width="10%" >CSE</th>
									<th width="10%" >CSE Distr</th>
									<th width="10%" >Date of Eval.</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(evaluation, index) in evaluations" :key="index">
									<td>{{ evaluation.invoice_number }}</td>
									<td>{{ evaluation.invoice_date }}</td>
									<td>{{ evaluation.evaluator_name }}</td>
									<td>{{ evaluation.student_name }}</td>
									<td>{{ evaluation.student_dob }}</td>
									<td>{{ evaluation.school_name }}</td>
									<td>{{ evaluation.language }}</td>
									<td>{{ evaluation.csr }}</td>
									<td>{{ evaluation.cse_district }}</td>
									<td>{{ evaluation.evaluation_date | formatDate }}</td>
								</tr>
								<tr>
									<td colspan="10" v-if="evaluations.length == 0">
										<center><strong>No Record Found.</strong></center>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script src="./rpt-assigned.js" />
