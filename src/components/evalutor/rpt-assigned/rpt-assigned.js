
import { paginationMixin } from "@/mixins/paginationMixin";
import Datepicker from 'vuejs-datepicker';
export default {
	data: () => ({
		selectedEvaluator:{},
		evaluator:{},
		comments:"",
		evaluators: [],
		evaluatorList:[],
		statesList:[],
		evaluations:[],
		error: "",
		searchText: "",
		showLoader: false,
		fromDate : new Date(),
		toDate : new Date(),
	}),
	components: {
		Datepicker
	},
	mixins: [paginationMixin],
	computed: {
		selectedStudent() {
			return this.evaluations.filter(element => element.isSelected==true);
		}
	},
	methods: {
		resetForm() {
			this.$refs.observer.reset();
			this.evaluator = {
				id: 0,
			};
		},
		convertDate(value){
			const date = new Date(value);
			return date.getFullYear() + "-" + ((date.getMonth() + 1) > 9 ? '' : '0') + (date.getMonth() + 1) +"-"+ (date.getDate() > 9 ? '' : '0') + date.getDate();
		},
		changeEvaluator(){
			this.evaluator = this.evaluatorList.find(element=> element.id == this.selectedEvaluator)
			this.evaluator.status=this.evaluator.status_id;

			console.log('inside changeEvaluator: ',this.evaluator);
		},

		getAllEvaluators() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_evaluators.php")
				.then(function(response) {
					_vm.showLoader = false;
					_vm.evaluatorList = response.data.data;
					_vm.selectedEvaluator = _vm.evaluatorList[0];
					_vm.changeEvaluator();

				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getOpenEvaluations() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_report_assigned_cases_by_evaluator.php?from_date="+this.convertDate(this.fromDate)+"&to_date="+ this.convertDate(this.toDate)+"&evaluator_id="+this.evaluator.id)
				.then(function(response) {
					_vm.showLoader = false;
					_vm.evaluations = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},

	},
	mounted() {
		this.getAllEvaluators();
		this.getOpenEvaluations();
	}
};
