<template>
    <div>
        <div class="row form-group">
            <h2 class="title-2 col-md-6 header-title">Import DOE Payments</h2>
        </div>
        <div class="card">
            <div class="card-body form-group">
                <input
					type="file"
					id="file-input"
					name="file-input"
					ref="inputFile"
					class="form-control-file"
					@change="onFileChange(...arguments)"
				/>
                <br />
                <button id="submit" @click="upload()" class="btn btn-outline-success btn-success mr-2">
                    Import Data
                </button>
                <!-- <a class="btn btn-warning" href="javascript:;" @click="exportData()">Export Data</a> -->
            </div>
        </div>
    </div>
</template>
<script>
export default {
	data: () => ({
	    selectedFile: []
	}),
	methods: {
        onFileChange(e) {
			var files = e.target.files || e.dataTransfer.files;
			if (!files.length) {
				return;
			}
			this.selectedFile = [];
			Array.prototype.forEach.call(files, item => {
				this.selectedFile.push(item);
			});
		},
			upload() {
			let _vm = this;
			let fd = new FormData();
            if(this.selectedFile.length==0){
                this.$dialog
				.confirm("Please select file first!");
                return;
            }
			fd.append("file", this.selectedFile[0]);
			this.axios.post("/set_import_doe_payments.php", fd).then(() => {
				_vm.selectedFile = [];
				_vm.$refs.inputFile.reset();
			});
		},
    }
};

</script>

