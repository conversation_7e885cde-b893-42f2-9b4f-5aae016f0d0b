
// import headerList from "@/components/common/header-list.vue";
import { paginationMixin } from "@/mixins/paginationMixin";
export default {
	data: () => ({
		selectedEvaluator:0,
		evaluator:{},
		evaluatorList: [],
		statesList:[],
		evaluatorTypeList:[],
		evaluatorLanguagesList:[],
		evaluatorBoroughsList:[],
		error: "",
		searchText: "",
		showLoader: false,
		isAllowToAdd:true
	}),
	mixins: [paginationMixin],
	// components: { "header-list": headerList },
	methods: {
		resetForm() {
			this.$refs.observer.reset();
			this.evaluator = {
				id: 0,
			};
			this.addNewProvider();
			this.isAllowToAdd= true;
		},
		validateData() {
			this.evaluator.id > 0 ? this.update() : this.add();
		},
		addNewProvider(){
			this.isAllowToAdd= false;
			this.selectedEvaluator=0;
			this.evaluator={};
			this.evaluator.status = '1';
			this.evaluator.type_id = '1';
			this.evaluator.borough_id = '1';
			this.evaluator.language_id = '1';
			this.evaluator.state = '1';
			this.evaluator.gender = 'F';


		},
		update() {
			let _vm = this;
			let fd = new FormData();

			Object.keys(_vm.evaluator).forEach(key => {
				fd.append(key, _vm.evaluator[key]);
			});
			this.axios
				.post("/set_evaluator.php",fd)
				.then(function() {
					_vm.getAllEvaluators();
					_vm.isAllowToAdd= true;
					//_vm.evaluator = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		add() {
			let _vm = this;
			let fd = new FormData();

			Object.keys(_vm.evaluator).forEach(key => {
				fd.append(key, _vm.evaluator[key]);
			});
			this.axios
				.post("/set_evaluator.php", fd)
				.then(function(response) {
					_vm.selectedEvaluator= parseInt(response.data.data.id);
					_vm.getAllEvaluators(_vm.selectedEvaluator);
					_vm.isAllowToAdd= true;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		changeEvaluator(){
			this.evaluator = this.evaluatorList.find(element=> element.id == this.selectedEvaluator)
			this.evaluator.status=this.evaluator.status_id;
		},
		getAllEvaluators(id) {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_evaluators.php")
				.then(function(response) {
					_vm.showLoader = false;
					_vm.evaluatorList = response.data.data;
					if(!id){
						_vm.selectedEvaluator=_vm.evaluatorList[0].id;
					}
					//_vm.selectedEvaluator.status=_vm.evaluatorList[0].status_id;
					_vm.changeEvaluator();
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getAllEvaluatorTypes() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_evaluator_types.php")
				.then(function(response) {
					_vm.showLoader = false;
					_vm.evaluatorTypeList = response.data.data;

				})
				.catch(function(error) {
					console.log(error);
				});
		},

		getEvaluatorLanguages() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_evaluator_languages.php")
				.then(function(response) {
					_vm.showLoader = false;
					_vm.evaluatorLanguagesList = response.data.data;

				})
				.catch(function(error) {
					console.log(error);
				});
		},

		getEvaluatorBoroughsList() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_evaluator_boroughs.php")
				.then(function(response) {
					_vm.showLoader = false;
					_vm.evaluatorBoroughsList = response.data.data;

				})
				.catch(function(error) {
					console.log(error);
				});
		},

		getAllStates() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_states.php")
				.then(function(response) {
					_vm.showLoader = false;
					_vm.statesList = response.data.data;

				})
				.catch(function(error) {
					console.log(error);
				});
		}
	},
	mounted() {
		this.getAllEvaluators();
		this.getAllEvaluatorTypes();
		this.getAllStates();
		this.getEvaluatorLanguages();
		this.getEvaluatorBoroughsList();

	}
};
