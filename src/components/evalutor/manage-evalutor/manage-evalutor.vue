<template>
	<div>
		<div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Edit Evaluator</h2>
		</div>
		<div class="card">
			<ValidationObserver ref="observer" v-slot="{ handleSubmit }">
				<form @submit.prevent="handleSubmit(validateData)">
					<div class="card-body form-group">
						<div class="row">
							<div class="col-6 col-md-2">
								<label for="type">Select Evaluator:<span class="text-danger"
										style="margin-top: 20px;width: 130px;"></span></label>
							</div>
							<div class="col-6 col-md-2 form-group">
								<select class="au-input au-input--full form-control" id="type" name="type"
									v-model="selectedEvaluator" @change="changeEvaluator()">
									<option v-for="(evaluator, index) in evaluatorList" :key="index"
										:value="evaluator.id">
										{{ evaluator.last_name }} {{ evaluator.first_name }}
									</option>
								</select>
							</div>
							<div class="col-12 col-md-4">
								<button class="btn btn-outline-primary" :disabled="!isAllowToAdd" @click="addNewProvider()">Add New Evaluator</button>
							</div>
						</div>
						<hr />

						<div class="row">
							<div class="col-md-2 form-group">
								<label for="type">Evaluator Type:<span class="text-danger"></span></label>
								<select class="au-input au-input--full form-control" v-model="evaluator.type_id"
									id="type" name="type">
									<option v-for="(evaluatorType, index) in evaluatorTypeList" :key="index"
										:value="evaluatorType.id">
										{{ evaluatorType.evl_type_description }}
									</option>
								</select>
							</div>
							<div class="col-md-1 form-group">
								<label for="type">Status:<span class="text-danger"></span></label>
								<select class="au-input au-input--full form-control" v-model="evaluator.status"
									id="status" name="status">
									<option value="1">Active</option>
									<option value="2">InActive</option>
								</select>
							</div>
							<div class="col-md-1 col-12 form-group">
								<label for="	type">Gender:<span class="text-danger"></span></label>
								<select class="au-input au-input--full form-control" id="gender"
									v-model="evaluator.gender" name="gender">
									<option value="M">Male</option>
									<option value="F">Female</option>
								</select>
							</div>
						</div>
						<div class="row">
							<div class="col-md-2 col-12  form-group">
								<label for="fname" class="form-label">First Name:<span
										class="text-danger"></span></label>
								<input type="text" name="first_name" v-model="evaluator.first_name" required
									class="au-input au-input--full form-control" id="first_name" />
							</div>

							<div class="col-md-2 col-12 form-group">
								<label for="lname" class="form-label">Last Name:<span
										class="text-danger"></span></label>
								<input type="text" name="last_name" v-model="evaluator.last_name" required
									class="au-input au-input--full form-control" id="last_name" />
							</div>
						</div>
						<div class="row">
							<div class="col-md-2 col-12 form-group">
								<label for="add_1" class="form-label">Address 1:<span
										class="text-danger"></span></label>
								<input type="text" name="street_1" v-model="evaluator.street_1"
									class="au-input au-input--full form-control" id="street_1" />
							</div>

							<div class="col-md-2 col-12 form-group">
								<label for="add_2" class="form-label">Address 2:<span
										class="text-danger"></span></label>
								<input type="text" name="street_2" v-model="evaluator.street_2"
									class="au-input au-input--full form-control" id="street_2" />
							</div>
							<div class="col-md-1 col-12 form-group">
								<label for="type">Evaluator Borough:<span class="text-danger"></span></label>
								<select class="au-input au-input--full form-control" v-model="evaluator.borough_id"
									id="type" name="type">
									<option v-for="(evaluatorBorough, index) in evaluatorBoroughsList" :key="index"
										:value="evaluatorBorough.id">
										{{ evaluatorBorough.borough_name }}
									</option>
								</select>
							</div>

						</div>
						<div class="row">

							<div class="col-md-2 col-12 form-group">
								<label for="city" class="form-label">City:<span class="text-danger"></span></label>
								<input type="text" name="city" v-model="evaluator.city" required
									class="au-input au-input--full form-control" id="city" />
							</div>

							<div class="col-md-1 col-12 form-group">
								<label for="type">State:<span class="text-danger"></span></label>
								<select class="au-input au-input--full form-control" id="state"
									v-model="evaluator.state" name="state">
									<option v-for="(state, index) in statesList" :key="index" :value="state.id">
										{{ state.state }}
									</option>
								</select>
							</div>
							<div class="col-md-1 col-12 form-group">
								<label for="zip" class="form-label">Zip:<span class="text-danger"></span></label>
								<input type="text" name="zip_code" v-model="evaluator.zip_code"
									class="au-input au-input--full form-control" id="zip_code" />
							</div>
							<div class="col-md-1 col-12 form-group">
								<label for="type">Evaluator Language:<span class="text-danger"></span></label>
								<select class="au-input au-input--full form-control" v-model="evaluator.language_id"
									id="type" name="type">
									<option v-for="(evaluatorLanguage, index) in evaluatorLanguagesList" :key="index"
										:value="evaluatorLanguage.id">
										{{ evaluatorLanguage.language_name }}
									</option>
								</select>
							</div>

						</div>
						<div class="row">

							<div class="col-md-4 col-6 form-group">
								<label for="email" class="form-label">Email:<span class="text-danger"></span></label>
								<input type="email" name="email" v-model="evaluator.email" required
									class="au-input au-input--full form-control" id="email" />
							</div>

							<div class="col-md-1 col-6 form-group" >
								<label for="phone" class="form-label">Phone:<span class="text-danger"></span></label>
								<input type="text" name="mobile_phone" v-model="evaluator.mobile_phone" required
									class="au-input au-input--full form-control" id="mobile_phone" />
							</div>
						</div>
						<div class="row">
							<div class="col-md-5 col-12 form-group">
									<label>Evaluator Comments</label>
									<textarea class="au-input au-input--full form-control" rows="3" name="comments" v-model="evaluator.comments"></textarea>
							</div>
						</div>
					</div>
					<div class="card-footer">
						<button type="submit" class="btn border-none btn-outline-primary btn-sm">
							<i class="fas fa-save"></i>&nbsp; Save</button>&nbsp;&nbsp;&nbsp;&nbsp;
						<button type="button" class="btn border-none btn-outline-danger btn-sm" @click="resetForm">
							<i class="fas fa-sync-alt"></i> Reset
						</button>
					</div>
				</form>
			</ValidationObserver>
		</div>
	</div>
</template>
<script src="./manage-evalutor.js" />
