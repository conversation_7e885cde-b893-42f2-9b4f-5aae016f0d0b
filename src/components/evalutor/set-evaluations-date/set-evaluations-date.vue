<template>
	<div>
		<div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Set Evalution Date</h2>
		</div>
		<div class="card">
			<div class="card-body p-0" >
				<div class="form-group p-4 row">
					<div class="col-md-2 sm-pl-0">
						<label>Select Evaluation Date:</label>
					</div>
						<datepicker placeholder="From Date" calendar-button calendar-button-icon="fa fa-calendar" format="MM/dd/yyyy" v-model="fromDate" class="form-control col-md-1"></datepicker>
				</div>
				<table class="table table-striped">
					<thead>
						<tr>
							<th width="5%" scope="col"></th>
							<th width="25%" >Student</th>
							<th width="20%" >School</th>
							<th width="20%" >CSE Distr</th>
							<th width="15%" >CSE</th>
							<th width="15%" >Compl. Date</th>
						</tr>
					</thead>
				</table>
				<div class="scroll-table">
					<table class="table table-striped">
						<tbody :class="evaluations.length == 0?'w-100 d-inline-block':''">
							<tr v-for="(evaluation, index) in evaluations" :key="index">
								<td width="5%"><input type="checkbox" name="evaluation[]" v-model="evaluation.isSelected"
										:value="evaluation.id">
								</td>
								<td width="25%">{{ evaluation.student_name }}</td>
								<td width="20%">{{ evaluation.school_name }}</td>
								<td width="20%">{{ evaluation.district_name }}</td>
								<td width="15%">{{ evaluation.cse}}</td>
								<td width="15%">{{ evaluation.compliance_date | formatDate }}</td>
							</tr>
							<tr class="w-100 d-inline-block">
								<td colspan="6" class="w-100 d-inline-block" v-if="evaluations.length == 0">
									<center><strong>No Record Found.</strong></center>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div class="p-4">
					<input v-if="selectedStudent.length > 0" type="submit" id="submit" class="btn btn-outline-primary"
						value="Set Evalution Date" @click="submit()">
				</div>
			</div>
		</div>
	</div>
</template>
<script src="./set-evaluations-date.js" />
