<template>
	<div>
		<div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Cases Past Compliance</h2>
		</div>
		<div class="card">
			<div class="card-body p-0">
				<div class="form-group" style="overflow:auto">
					<table class="table table-striped">
						<thead>
							<tr>
								<th width="15%" >Evaluator</th>
								<th width="20%" >Student</th>
								<th width="10%" >Student DOB</th>
								<th width="15%" >School</th>
								<th width="10%" >Language</th>
								<th width="10%" >CSE</th>
								<th width="10%" >CSE Distr</th>
								<th width="10%" >Date of Eval.</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="(evaluation, index) in evaluations" :key="index">
								<td>{{ evaluation.evaluator_name }}</td>
								<td>{{ evaluation.student_name }}</td>
								<td>{{ evaluation.student_dob }}</td>
								<td>{{ evaluation.school_name }}</td>
								<td>{{ evaluation.language }}</td>
								<td>{{ evaluation.cse }}</td>
								<td>{{ evaluation.district_name }}</td>
								<td>{{ evaluation.compliance_date }}</td>
							</tr>
							<tr>
								<td colspan="8" v-if="evaluations.length == 0">
									<center><strong>No Record Found.</strong></center>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</template>
<script src="./rpt-past-compliance.js" />