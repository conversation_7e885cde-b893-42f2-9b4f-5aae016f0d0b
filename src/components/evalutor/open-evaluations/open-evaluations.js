
// import headerList from "@/components/common/header-list.vue";
import { paginationMixin } from "@/mixins/paginationMixin";
import { _ } from "vue-underscore";
export default {
	data: () => ({
		selectedEvaluator:{},
		evaluator:{},
		evaluators: [],
		statesList:[],
		evaluations:[],
		tableData:[],
		error: "",
		searchText: "",
		showLoader: false
	}),
	mixins: [paginationMixin],
	// components: { "header-list": headerList },
	computed: {
		selectedStudent() {
			return this.evaluations.filter(element => element.isSelected==true);
		}
	},
	methods: {
		searchData(){
			const _vm =this;
			this.tableData = _.filter(this.evaluations,function(element){
				return (element.student_name.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1) ||
				(element.district_name.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1) ||
				(element.school_name.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1) ||
				(element.student_dob.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1) ||
				(element.compliance_date.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1) ||
				(element.zip_code.toLowerCase().indexOf(_vm.searchText.toLowerCase())>-1);
			})
		},

		resetForm() {
			this.$refs.observer.reset();
			this.evaluator = {
				id: 0,
			};
		},
		validateData() {
			this.evaluator.id > 0 ? this.update() : this.add();
		},
		addNewProvider(){
			this.evaluator={};
		},
		sendEmail() {
			let _vm = this;
			let fd = new FormData();
			let list = [];
			this.selectedStudent.forEach(element => {
				list.push(element.id);
			});
			fd.append("evaluator_id",this.selectedEvaluator.id);
			fd.append("evaluation_ids",list);
			this.axios
				.post("/set_email_open_evaluations.php", fd)
				.then(function() {
					_vm.getAllEvaluators();
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getAllEvaluators() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_evaluators.php")
				.then(function(response) {
					_vm.showLoader = false;
					_vm.evaluators = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		getOpenEvaluations() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_open_evaluations.php")
				.then(function(response) {
					_vm.showLoader = false;
					response.data.data.forEach(element => {
						element["isSelected"] = false;
					});
					_vm.evaluations = response.data.data;
					_vm.tableData = response.data.data;

				})
				.catch(function(error) {
					console.log(error);
				});
		},
		closeCanvas(){
			var elm = $(this).hasClass('bs-canvas-close') ? $(this).closest('.bs-canvas') : $('.bs-canvas');
			elm.removeClass('mr-0 ml-0');
			$('.bs-canvas-overlay').remove();
		}

	},
	mounted() {
		this.getAllEvaluators();
		this.getOpenEvaluations();
		$(document).ready(function ($) {
			$(document).on('click', '.pull-bs-canvas-right', function () {
				$('#app').prepend('<div class="bs-canvas-overlay bg-dark position-fixed w-100 h-100"></div>');
				if ($(this).hasClass('pull-bs-canvas-right'))
					$('.bs-canvas-right').addClass('mr-0');
				else
					$('.bs-canvas-left').addClass('ml-0');
					$("#rightCanvas").detach().appendTo("#app");
				return false;
			});

			$(document).on('click', '.bs-canvas-close, .bs-canvas-overlay', function () {
				var elm = $(this).hasClass('bs-canvas-close') ? $(this).closest('.bs-canvas') : $('.bs-canvas');
				elm.removeClass('mr-0 ml-0');
				$('.bs-canvas-overlay').remove();
				return false;
			});
		});
	}
};
