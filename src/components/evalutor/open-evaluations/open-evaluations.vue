<template>
	<div id="openEvalutions">
		<!-- <div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Open Evaluations</h2>
		</div> -->
		<div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Open Evaluations</h2>
			<div class="col-md-6 text-right">
				<div class="input-group input-group w-50 float-right">
					<input type="text" v-model="searchText" v-on:keyup.enter="searchData" class="form-control au-input" placeholder="Search ...">
					<div class="input-group-append bg-white">
						<button @click="searchData()" class=" btn btn-outline-primary" id="basic-addon2"><i
								class="fas fa-search"></i></button>
					</div>
				</div>
			</div>
		</div>
		<div class="card">
			<div class="card-body">
				<div class="row pt-2 pl-3">
					<div class="co-md-8">
						<button type="button" class="btn btn-outline-primary pull-bs-canvas-right"
							data-toggle="collapse" data-target="#collapseBlock">Candidates Selection</button>
						<span id="demo" class="ml-4">
							<template v-if="Object.keys(selectedEvaluator).length > 0">
								Evaluator Name: <span class="text-danger">{{ selectedEvaluator.first_name }}
									{{ selectedEvaluator.last_name }}</span> Email: <span
									class="text-danger">{{ selectedEvaluator.email }}</span>
							</template>
							<span v-else class="text-danger">Candidate is not selected</span>
						</span>
					</div>
					<div class="co-md-4">
						<input v-if="selectedStudent.length > 0 && Object.keys(selectedEvaluator).length > 0" type="submit"
							id="submit" class="mr-2 ml-2 btn btn-outline-primary float-right" value="Send Email Request"
							@click="sendEmail()">
					</div>
				</div>
				<br>

				<div class="card">
					<div class="card-body p-0" style="overflow:auto">
						<div class="card-header text-center">
							<h5 class="text-black fw-bold mb-0">Open Evaluations</h5>
						</div>
						<div>
							<table class="table table-striped ">
								<thead>
									<tr>
										<th width="5%" scope="col"></th>
										<th width="25%" scope="col">Student</th>
										<th width="10%" scope="col">DOB</th>
										<th width="25%" scope="col">School</th>
										<th width="10%" scope="col">Zip Code</th>
										<th width="15%" scope="col">District</th>
										<th width="10%" scope="col">Compl. Date</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="(evaluation, index) in tableData" :key="index">
										<td><input type="checkbox" name="evaluation[]" v-model="evaluation.isSelected"
												:value="evaluation.id">
										</td>
										<td>{{ evaluation.student_name }}</td>
										<td>{{ evaluation.student_dob }}</td>
										<td>{{ evaluation.school_name }}</td>
										<td>{{ (evaluation.zip_code) ? evaluation.zip_code : '' }}
										</td>
										<td>{{ evaluation.district_name }}</td>
										<td>{{ evaluation.compliance_date | formatDate }}</td>
									</tr>
									<tr>
										<td colspan="7" v-if="evaluations.length == 0">
											<center><strong>No Record Found.</strong></center>
										</td>
									</tr>
								</tbody>
							</table>
							<!-- <div class="pagination justify-content-end mr-2">
								{{ $evaluations -> links() }}
							</div> -->
							<input v-if="selectedStudent.length > 0 && Object.keys(selectedEvaluator).length > 0"
								type="submit" id="submit" class="mr-4 mb-4 mt-4 btn btn-outline-primary float-right"
								value="Send Email Request" @click="sendEmail()">
							<!-- End Table with stripped rows -->
						</div>
					</div>
				</div>
			</div>
		</div>
		<div id="rightCanvas" class="bs-canvas bs-canvas-right position-fixed bg-light h-100">
			<div class="bs-canvas-content px-3 py-5" style="overflow:auto">
				<table class="table table-striped ">
					<thead>
						<tr>
							<th width="10%" scope="col">Select</th>
							<th width="50%" scope="col">Evaluator</th>
							<th width="20%" scope="col">Zip Code</th>
							<th width="20%" scope="col">Email</th>
						</tr>
					</thead>
					<tbody>
						<template v-if="evaluators && evaluators.length > 0">
							<tr v-for="(evaluator, index) in evaluators" :key="index">
								<td><label for="type"></label><input v-model="selectedEvaluator" @click="closeCanvas()"
										type="radio" name="evaluator" :value="evaluator"></td>
								<td><label for="fname"></label>{{ evaluator.first_name }} <label for="lname"></label>{{
										evaluator.last_name
								}} </td>
								<td><label for="zipcode"></label>{{ evaluator.zip_code }}</td>
								<td><label for="email"></label>{{ evaluator.email }}</td>
							</tr>
						</template>
						<tr v-else>
							<td colspan="4">
								<center><strong>No Record Found.</strong></center>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script src="./open-evaluations.js" />
