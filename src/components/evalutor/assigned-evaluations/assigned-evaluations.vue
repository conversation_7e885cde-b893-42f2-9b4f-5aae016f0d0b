<template>
	<div>
		<div class="row form-group">
			<h2 class="title-2 col-md-6 header-title">Assigned Evaluations</h2>
		</div>
		<div class="card">
			<div class="card-body p-0" style="overflow:auto">
				<table class="table table-striped ">
					<thead>
						<tr>
							<th width="15%" >Student</th>
							<th width="15%" >School</th>
							<th width="15%" >CSE District</th>
							<th width="15%" >CSE</th>
							<th width="10%" >Compl. Date</th>
							<th width="10%" >Eval. Date</th>
							<th width="20%" >Rpt. Stauts</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="(evaluation, index) in evaluations" :key="index">
							<td>{{ evaluation.student_name }}</td>
							<td>{{ evaluation.school_name }}</td>
							<td>{{ evaluation.district_name }}</td>
							<td>{{ evaluation.cse }}</td>
							<td>{{ evaluation.compliance_date | formatDate }}</td>
							<td>{{ evaluation.evaluation_date | formatDate }}</td>
							<td>{{ evaluation.report_status }}</td>
						</tr>
						<tr>
							<td colspan="7" v-if="evaluations.length == 0">
								<center><strong>No Record Found.</strong></center>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script src="./assigned-evaluations.js" />