
import { paginationMixin } from "@/mixins/paginationMixin";
import Datepicker from 'vuejs-datepicker';
export default {
	data: () => ({
		selectedEvaluator:{},
		evaluator:{},
		comments:"",
		evaluators: [],
		statesList:[],
		evaluations:[],
		error: "",
		searchText: "",
		showLoader: false,
		fromDate : new Date(),
		toDate : new Date(),
	}),
	components: {
		Datepicker
	},
	mixins: [paginationMixin],
	computed: {
		selectedStudent() {
			return this.evaluations.filter(element => element.isSelected==true);
		}
	},
	methods: {
		resetForm() {
			this.$refs.observer.reset();
			this.evaluator = {
				id: 0,
			};
		},
		cancelEvalutions() {
			let _vm = this;
			let fd = new FormData();
			let list = [];
			this.selectedStudent.forEach(element => {
				list.push(element.id);
			});
			fd.append("comments",this.comments);
			fd.append("evaluation_ids",list.join(','));
			this.axios
				.post("/set_cancel_evaluations.php", fd)
				.then(function() {
					_vm.comments="";
					_vm.getOpenEvaluations();
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		convertDate(value){
			const date = new Date(value);
			return date.getFullYear() + "-" + ((date.getMonth() + 1) > 9 ? '' : '0') + (date.getMonth() + 1) +"-"+ (date.getDate() > 9 ? '' : '0') + date.getDate();
		},
		getOpenEvaluations() {
			let _vm = this;
			_vm.showLoader = true;
			this.axios
				.get("/get_report_cases_paid_by_doe.php?from_date="+this.convertDate(this.fromDate)+"&to_date="+ this.convertDate(this.toDate))
				.then(function(response) {
					_vm.showLoader = false;
					response.data.data.forEach(element => {
						element["isSelected"] = false;
					});
					_vm.evaluations = response.data.data;
				})
				.catch(function(error) {
					console.log(error);
				});
		},
		
	},
	mounted() {
		this.getOpenEvaluations();
	}
};
