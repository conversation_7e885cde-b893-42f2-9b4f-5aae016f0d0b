.invalid-feedback {
	display: block;
}
.custom-select.is-valid,
.form-control.is-valid,
.was-validated .custom-select:valid,
.was-validated .form-control:valid {
	border-color: #e5e5e5;
}
.search-box {
	width: 65%;
	margin-right: 10px;
	display: inline-block;
}
.btn-add-new {
	width: 30%;
	float: right;
	display: inline-block;
}
.theme-color {
	color: #007bff !important;
}
.theme-trash {
	color: red !important;
}
.back-btn {
	float: right;
}

.loader {
	border: 16px solid #333333;
	border-radius: 50%;
	border-top: 16px solid #007bff;
	border-bottom: 16px solid #007bff;
	width: 95px;
	height: 95px;
	-webkit-animation: spin 2s linear infinite;
	animation: spin 2s linear infinite;
	position: fixed;
	left: 45%;
	top: 45%;
	z-index: 99999999;
}

@-webkit-keyframes spin {
	0% {
		-webkit-transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

.opacity-6 {
	opacity: 0.7;
}

.alert {
	display: none;
	border-radius: 0;
	color: #fff;
	text-align: left;
	position: fixed;
	top: 0;
	z-index: 9999;
	right: 0;
	margin: 10px;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
	width: 90%;
	max-width: 380px;
	min-width: 260px;
	font-size: 18px;
	min-height: 60px;
	line-height: 1.6em;
}

.alert-success {
	background-color: #33b575;
	border-color: #33b575;
}

.alert-danger {
	background-color: #f56666;
	border-color: #f56666;
}

.alert-dismissable .close,
.alert-dismissible .close {
	opacity: 0.4;
}

/* Profile image css */
.avatar-upload {
	position: relative;
	max-width: 205px;
	margin: 50px auto;
}
.avatar-upload .avatar-edit {
	position: absolute;
	right: 25px;
	z-index: 1;
	top: 10px;
}
.avatar-upload .avatar-edit input {
	display: none;
}
.avatar-upload .avatar-edit input + label {
	display: inline-block;
	width: 34px;
	height: 34px;
	margin-bottom: 0;
	border-radius: 100%;
	background: #495057;
	border: 1px solid transparent;
	box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12);
	cursor: pointer;
	font-weight: normal;
	transition: all 0.2s ease-in-out;
}
.avatar-upload .avatar-edit input + label:hover {
	background: #f1f1f1;
	border-color: #d6d6d6;
}
.image-edit-pencil {
	margin: 8px;
}
.avatar-upload .avatar-preview {
	width: 192px;
	height: 192px;
	position: relative;
	border-radius: 100%;
	border: 6px solid #495057;
	box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
}
.avatar-upload .avatar-preview > div {
	width: 100%;
	height: 100%;
	border-radius: 100%;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
}

.ui-datepicker-calendar td a,
.ui-datepicker-calendar td span {
	width: 100%;
}
textarea {
	resize: none;
 }

 .scroll-table{
	height: 70vh;
    display: inline-block;
    width: 100%;
    overflow-x: scroll;
 }
 ::-webkit-scrollbar {
	width: 1px;
  }


 .bs-canvas-overlay {
	opacity: 0.5;
 	z-index: 1100;
}

.bs-canvas {
 top: 0;
 z-index: 1110;
 overflow-x: hidden;
 overflow-y: auto;
 width: 600px;
 transition: margin .4s ease-out;
 -webkit-transition: margin .4s ease-out;
 -moz-transition: margin .4s ease-out;
 -ms-transition: margin .4s ease-out;
}

.bs-canvas-left {
 left: 0;
 margin-left: -330px;
}

.bs-canvas-right {
 right: 0;
 margin-right: -600px;
}

/* Only for demo */
body {
 	min-height: 100vh;
}
@media (max-width: 767px) {
	.bs-canvas {
		width: 100% !important;
	}
}