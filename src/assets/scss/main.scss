body {
	&.light {
		$header-bg: #f5f5f5;
		$theme-container: #e5e5e5;
		$theme-bg: #fff;
		$theme-card: #fff;
		$theme-control: #ffffff;
		$theme-control-border: #e5e5e5;
		$theme-font: #666;
		$theme-font-title: #333333;
		$pagination-bg: #fff;
		$pagination-selected-font: #7a7a7a;
		$mobile-menu-li: #f8f8f8;
		$menu-li-font: #555;
		$mobile-menu-btn: #4d4d4d;
		$account-drop-down-font: #333;
		font-family: "Poppins", sans-serif;
		font-weight: 400;
		font-size: 14px;
		line-height: 1.625;
		color: $theme-font;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		@import "sass";
		@import "style";
	}

	&.dark {
		$header-bg: #1c1f23;
		$theme-container: #333940;
		$theme-bg: #212529;
		$theme-card: #212529;
		$theme-control: #333940;
		$theme-control-border: #1c1f23;
		$theme-font: #9ea3a7;
		$theme-font-title: #9ea3a7;
		$pagination-bg: #333940;
		$pagination-selected-font: #ffffff;
		$mobile-menu-li: #1c1f23;
		$menu-li-font: #9ea3a7;
		$mobile-menu-btn: #9ea3a7;
		$account-drop-down-font: #9ea3a7;
		font-family: "Poppins", sans-serif;
		font-weight: 400;
		font-size: 14px;
		line-height: 1.625;
		color: $theme-font;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		@import "sass";
		@import "style";
	}
}

@media (max-width: 768px) {
	body {
		&.light {
			font-size: 12px !important;
		}
		&.dark {
			font-size: 12px !important;
		}
	}
}
